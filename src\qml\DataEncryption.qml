import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Controls.Material 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs
import DataProtection 1.0

ScrollView {
    id: root
    anchors.fill: parent
    contentWidth: availableWidth

    // 连接全局数据加密引擎的信号
    Connections {
        target: dataEncryptionEngine

        function onEncryptionProgress(taskId, progress) {
            console.log("加密进度更新:", taskId, progress + "%")
            updateTaskProgress(taskId, progress)
        }

        function onEncryptionCompleted(taskId, success, message) {
            console.log("加密完成:", taskId, success, message)
            if (success) {
                showSuccessMessage("文件加密成功: " + message)
                refreshEncryptedFilesList()
                refreshStatistics()
            } else {
                showErrorMessage("加密失败: " + message)
            }
        }

        function onFileEncrypted(originalPath, encryptedPath) {
            console.log("文件已加密:", originalPath, "->", encryptedPath)
            refreshEncryptedFilesList()
        }

        function onFileDecrypted(encryptedPath, decryptedPath) {
            console.log("文件已解密:", encryptedPath, "->", decryptedPath)
            showSuccessMessage("文件解密成功")
        }

        function onKeyGenerated(keyId, keyName) {
            console.log("密钥已生成:", keyId, keyName)
            showSuccessMessage("密钥生成成功: " + keyName)
        }

        function onErrorOccurred(message) {
            console.log("加密引擎错误:", message)
            showErrorMessage(message)
        }
    }

    // 文件对话框
    FileDialog {
        id: fileDialog
        title: "选择要加密的文件"
        fileMode: FileDialog.OpenFile
        onAccepted: {
            var filePath = selectedFile.toString().replace("file:///", "")
            filePathField.text = filePath
            console.log("选择文件:", filePath)
        }
    }

    FileDialog {
        id: folderDialog
        title: "选择要加密的文件夹"
        fileMode: FileDialog.OpenFolder
        onAccepted: {
            var folderPath = selectedFolder.toString().replace("file:///", "")
            filePathField.text = folderPath
            console.log("选择文件夹:", folderPath)
        }
    }

    // 消息提示
    function showSuccessMessage(message) {
        messageText.text = message
        messageText.color = "#27ae60"
        messageTimer.restart()
    }

    function showErrorMessage(message) {
        messageText.text = message
        messageText.color = "#e74c3c"
        messageTimer.restart()
    }

    Timer {
        id: messageTimer
        interval: 3000
        onTriggered: messageText.text = ""
    }

    // 刷新加密文件列表
    function refreshEncryptedFilesList() {
        var files = dataEncryptionEngine.getEncryptedFiles()
        encryptedFilesModel.clear()
        for (var i = 0; i < files.length; i++) {
            encryptedFilesModel.append(files[i])
        }
        console.log("刷新加密文件列表，共", files.length, "个文件")
    }

    // 刷新统计信息
    function refreshStatistics() {
        var stats = dataEncryptionEngine.getStatistics()
        encryptedFileCountText.text = "已加密文件: " + stats.totalEncryptedFiles + " 个"
        console.log("统计信息:", JSON.stringify(stats))
    }

    // 更新任务进度
    function updateTaskProgress(taskId, progress) {
        encryptionProgressBar.value = progress / 100.0
        encryptionProgressBar.visible = progress < 100
    }

    Component.onCompleted: {
        console.log("DataEncryption页面加载完成")
        refreshEncryptedFilesList()
        refreshStatistics()
    }

    ColumnLayout {
        width: parent.width
        anchors.margins: 20
        spacing: 20

        // 页面标题
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: Qt.color("white")
            radius: 8
            border.color: Qt.color("#ddd")

            RowLayout {
                anchors.fill: parent
                anchors.margins: 8

                Text {
                    text: "🔒 数据加密与脱敏"
                    font.pixelSize: 24
                    font.bold: true
                    color: Qt.color("#2c3e50")
                }

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "保护策略概览"
                    Material.background: Qt.color("#27ae60")
                    Material.foreground: Qt.color("white")
                    onClicked:
                    // 保护策略概览
                    {}
                }
            }
        }

        // 本地数据加密区域
        GroupBox {
            Layout.fillWidth: true
            title: "本地数据加密"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 加密设置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "加密算法:"
                        font.bold: true
                    }

                    ComboBox {
                        id: algorithmComboBox
                        Layout.preferredWidth: 150
                        model: ["AES-256", "AES-128", "ChaCha20", "3DES"]
                        currentIndex: 0
                        onCurrentTextChanged: {
                            if (dataEncryptionEngine) {
                                dataEncryptionEngine.currentAlgorithm = currentText
                                console.log("切换加密算法到:", currentText)
                            }
                        }
                    }

                    Text {
                        text: "密钥管理:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["本地密钥库"]
                        currentIndex: 0
                    }

                    Button {
                        text: "生成新密钥"
                        Material.background: Qt.color("#3498db")
                        Material.foreground: Qt.color("white")
                        onClicked: {
                            console.log("生成新密钥，算法:", algorithmComboBox.currentText)
                            var keyId = dataEncryptionEngine.generateNewKey("", algorithmComboBox.currentText)
                            if (keyId) {
                                console.log("密钥生成成功，ID:", keyId)
                            }
                        }
                    }
                }

                // 文件加密操作
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(250, encryptionLayout.height + 32)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        id: encryptionLayout
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 12

                        Text {
                            text: "文件加密操作"
                            font.bold: true
                            font.pixelSize: 16
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            TextField {
                                id: filePathField
                                Layout.fillWidth: true
                                placeholderText: "选择要加密的文件或文件夹..."
                                readOnly: true
                            }

                            Button {
                                text: "选择文件"
                                onClicked: {
                                    console.log("打开文件选择对话框")
                                    fileDialog.open()
                                }
                            }

                            Button {
                                text: "选择文件夹"
                                onClicked: {
                                    console.log("打开文件夹选择对话框")
                                    folderDialog.open()
                                }
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            CheckBox {
                                id: recursiveCheckBox
                                text: "递归加密子目录"
                                checked: true
                            }

                            CheckBox {
                                id: keepOriginalCheckBox
                                text: "保留原文件"
                                checked: false
                            }

                            CheckBox {
                                id: compressCheckBox
                                text: "压缩后加密"
                                checked: true
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Button {
                                text: "开始加密"
                                Material.background: Qt.color("#27ae60")
                                Material.foreground: Qt.color("white")
                                enabled: filePathField.text !== "" && !dataEncryptionEngine.isEncrypting
                                onClicked: {
                                    console.log("开始加密文件:", filePathField.text)
                                    var filePath = filePathField.text
                                    var isDirectory = Qt.resolvedUrl(filePath).toString().indexOf('.') === -1

                                    var taskId
                                    if (isDirectory) {
                                        taskId = dataEncryptionEngine.encryptDirectory(
                                            filePath, "", recursiveCheckBox.checked,
                                            keepOriginalCheckBox.checked, compressCheckBox.checked)
                                    } else {
                                        taskId = dataEncryptionEngine.encryptFile(
                                            filePath, "", keepOriginalCheckBox.checked,
                                            compressCheckBox.checked)
                                    }

                                    if (taskId) {
                                        console.log("加密任务已创建:", taskId)
                                        encryptionProgressBar.visible = true
                                    }
                                }
                            }

                            Button {
                                text: "批量解密"
                                Material.background: Qt.color("#e74c3c")
                                Material.foreground: Qt.color("white")
                                enabled: encryptedFilesModel.count > 0 && !dataEncryptionEngine.isEncrypting
                                onClicked: {
                                    console.log("开始批量解密")
                                    var selectedFiles = []
                                    for (var i = 0; i < encryptedFilesModel.count; i++) {
                                        selectedFiles.push(encryptedFilesModel.get(i).encryptedPath)
                                    }
                                    if (selectedFiles.length > 0) {
                                        var success = dataEncryptionEngine.batchDecrypt(selectedFiles, "")
                                        if (success) {
                                            showSuccessMessage("批量解密完成")
                                        }
                                    }
                                }
                            }

                            Item {
                                Layout.fillWidth: true
                            }

                            Text {
                                id: encryptedFileCountText
                                text: "已加密文件: 0 个"
                                color: Qt.color("#27ae60")
                                font.bold: true
                            }
                        }

                        ProgressBar {
                            id: encryptionProgressBar
                            Layout.fillWidth: true
                            value: 0.0
                            visible: false
                        }

                        // 消息显示区域
                        Text {
                            id: messageText
                            Layout.fillWidth: true
                            text: ""
                            color: Qt.color("#27ae60")
                            font.bold: true
                            visible: text !== ""
                        }
                    }
                }

                // 加密文件列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(200, encryptedListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: encryptedListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            id: encryptedFilesModel
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 35
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 12
                                spacing: 8

                                Text {
                                    text: "文件名"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "大小"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "加密日期"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "操作"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: encryptedFileDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 45
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 0

                                Text {
                                    text: encryptedFileDelegateItem.model.fileName || ""
                                    Layout.fillWidth: true
                                    font.pixelSize: 14
                                }
                                Text {
                                    text: encryptedFileDelegateItem.model.encryptedSize || ""
                                    Layout.preferredWidth: 80
                                    font.pixelSize: 14
                                }
                                Text {
                                    text: encryptedFileDelegateItem.model.encryptionDate || ""
                                    Layout.preferredWidth: 100
                                    font.pixelSize: 14
                                }
                                Text {
                                    text: encryptedFileDelegateItem.model.status || ""
                                    Layout.preferredWidth: 80
                                    font.pixelSize: 14
                                    color: Qt.color("#27ae60")
                                }

                                RowLayout {
                                    Layout.preferredWidth: 100

                                    Button {
                                        text: "解密"
                                        flat: true
                                        Material.foreground: Qt.color("#e74c3c")
                                        onClicked: {
                                            console.log("解密文件:", encryptedFileDelegateItem.model.encryptedPath)
                                            var success = dataEncryptionEngine.decryptFile(
                                                encryptedFileDelegateItem.model.encryptedPath || "", "")
                                            if (success) {
                                                console.log("文件解密成功")
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // 数据脱敏工具区域
        GroupBox {
            Layout.fillWidth: true
            title: "数据脱敏工具"

            ColumnLayout {
                anchors.fill: parent
                spacing: 12

                // 脱敏规则设置
                RowLayout {
                    Layout.fillWidth: true

                    Text {
                        text: "脱敏类型:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["掩码脱敏", "替换脱敏", "加密脱敏", "哈希脱敏"]
                        currentIndex: 0
                    }

                    Text {
                        text: "数据类型:"
                        font.bold: true
                    }

                    ComboBox {
                        Layout.preferredWidth: 150
                        model: ["身份证号", "手机号码", "邮箱地址", "API秘钥", "密码字段", "IP地址"]
                        currentIndex: 0
                    }

                    Button {
                        text: "自定义规则"
                        onClicked:
                        // 自定义脱敏规则
                        {}
                    }
                }

                // 脱敏预览
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(150, desensitizationPreview.height + 32)
                    border.color: Qt.color("#ddd")
                    radius: 4
                    color: Qt.color("#f8f9fa")

                    ColumnLayout {
                        id: desensitizationPreview
                        anchors.fill: parent
                        anchors.margins: 16
                        spacing: 8

                        Text {
                            text: "脱敏预览"
                            font.bold: true
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "原始数据:"
                                Layout.preferredWidth: 80
                                font.bold: true
                            }

                            Text {
                                text: "张三 | 13812345678 | 110101199001011234"
                                Layout.fillWidth: true
                                color: Qt.color("#e74c3c")
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Text {
                                text: "脱敏后:"
                                Layout.preferredWidth: 80
                                font.bold: true
                            }

                            Text {
                                text: "张** | 138****5678 | 110101********1234"
                                Layout.fillWidth: true
                                color: Qt.color("#27ae60")
                            }
                        }

                        RowLayout {
                            Layout.fillWidth: true

                            Button {
                                text: "应用脱敏"
                                Material.background: Qt.color("#f39c12")
                                Material.foreground: Qt.color("white")
                                onClicked:
                                // 应用脱敏
                                {}
                            }

                            Button {
                                text: "批量脱敏"
                                Material.background: Qt.color("#9b59b6")
                                Material.foreground: Qt.color("white")
                                onClicked:
                                // 批量脱敏
                                {}
                            }

                            Item {
                                Layout.fillWidth: true
                            }
                        }
                    }
                }

                // 脱敏任务列表
                Rectangle {
                    Layout.fillWidth: true
                    Layout.preferredHeight: Math.max(120, desensitizationListView.contentHeight + 16)
                    border.color: Qt.color("#ddd")
                    radius: 4

                    ListView {
                        id: desensitizationListView
                        anchors.fill: parent
                        anchors.margins: 8
                        model: ListModel {
                            ListElement {
                                name: "客户信息脱敏"
                                type: "掩码脱敏"
                                progress: 100
                                status: "已完成"
                            }
                            ListElement {
                                name: "员工档案脱敏"
                                type: "替换脱敏"
                                progress: 75
                                status: "进行中"
                            }
                            ListElement {
                                name: "订单数据脱敏"
                                type: "加密脱敏"
                                progress: 0
                                status: "等待中"
                            }
                        }

                        header: Rectangle {
                            width: parent.width
                            height: 25
                            color: Qt.color("#f1f1f1")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 6

                                Text {
                                    text: "任务名称"
                                    font.bold: true
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: "脱敏类型"
                                    font.bold: true
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: "进度"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: "状态"
                                    font.bold: true
                                    Layout.preferredWidth: 80
                                }
                            }
                        }

                        delegate: Rectangle {
                            id: desensitizationTaskDelegateItem
                            required property int index
                            required property var model
                            width: parent.width
                            height: 30
                            color: index % 2 === 0 ? Qt.color("#f8f9fa") : Qt.color("white")

                            RowLayout {
                                anchors.fill: parent
                                anchors.margins: 6

                                Text {
                                    text: desensitizationTaskDelegateItem.model.name
                                    Layout.fillWidth: true
                                }
                                Text {
                                    text: desensitizationTaskDelegateItem.model.type
                                    Layout.preferredWidth: 100
                                }
                                Text {
                                    text: desensitizationTaskDelegateItem.model.progress + "%"
                                    Layout.preferredWidth: 80
                                }
                                Text {
                                    text: desensitizationTaskDelegateItem.model.status
                                    Layout.preferredWidth: 80
                                    color: desensitizationTaskDelegateItem.model.status === "已完成" ? Qt.color("#27ae60") : (desensitizationTaskDelegateItem.model.status === "进行中" ? Qt.color("#f39c12") : Qt.color("#95a5a6"))
                                }
                            }
                        }
                    }
                }
            }
        }

        Item {
            Layout.preferredHeight: 20
        }
    }
}
