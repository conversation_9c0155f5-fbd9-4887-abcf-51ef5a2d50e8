#ifndef DATAENCRYPTIONENGINE_H
#define DATAENCRYPTIONENGINE_H

#include <QObject>
#include <QQmlEngine>
#include <QString>
#include <QStringList>
#include <QVariantMap>
#include <QVariantList>
#include <QFileInfo>
#include <QDir>
#include <QMutex>
#include <QThread>
#include <QTimer>
#include <QCryptographicHash>
#include <QRandomGenerator>
#include <QStandardPaths>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>

// 加密算法枚举
enum class EncryptionAlgorithm {
    AES_256,
    AES_128,
    ChaCha20,
    TripleDES
};

// 加密任务状态
enum class EncryptionTaskStatus {
    Pending,
    InProgress,
    Completed,
    Failed,
    Cancelled
};

// 加密文件信息结构
struct EncryptedFileInfo {
    QString originalPath;
    QString encryptedPath;
    QString fileName;
    qint64 originalSize;
    qint64 encryptedSize;
    QDateTime encryptionDate;
    EncryptionAlgorithm algorithm;
    QString keyId;
    EncryptionTaskStatus status;
    QString checksum;
};

// 加密任务信息结构
struct EncryptionTask {
    QString taskId;
    QString taskName;
    QStringList filePaths;
    QString outputDir;
    EncryptionAlgorithm algorithm;
    bool recursive;
    bool keepOriginal;
    bool compressFirst;
    EncryptionTaskStatus status;
    int progress;
    QDateTime startTime;
    QDateTime endTime;
    QString errorMessage;
    int totalFiles;
    int processedFiles;
};

// 密钥信息结构
struct KeyInfo {
    QString keyId;
    QString keyName;
    EncryptionAlgorithm algorithm;
    QDateTime creationDate;
    QDateTime lastUsed;
    bool isActive;
    QString description;
};

class DataEncryptionEngine : public QObject
{
    Q_OBJECT
    QML_ELEMENT

    Q_PROPERTY(bool isEncrypting READ isEncrypting NOTIFY isEncryptingChanged)
    Q_PROPERTY(int encryptedFileCount READ encryptedFileCount NOTIFY encryptedFileCountChanged)
    Q_PROPERTY(QString currentAlgorithm READ currentAlgorithm WRITE setCurrentAlgorithm NOTIFY currentAlgorithmChanged)
    Q_PROPERTY(QStringList availableAlgorithms READ availableAlgorithms CONSTANT)

public:
    explicit DataEncryptionEngine(QObject *parent = nullptr);
    ~DataEncryptionEngine();

    // 属性访问器
    bool isEncrypting() const { return m_isEncrypting; }
    int encryptedFileCount() const { return m_encryptedFiles.size(); }
    QString currentAlgorithm() const { return m_currentAlgorithm; }
    void setCurrentAlgorithm(const QString& algorithm);
    QStringList availableAlgorithms() const;

    // 密钥管理
    Q_INVOKABLE QString generateNewKey(const QString& keyName = "", const QString& algorithm = "AES-256");
    Q_INVOKABLE QVariantList getAvailableKeys() const;
    Q_INVOKABLE bool deleteKey(const QString& keyId);
    Q_INVOKABLE bool exportKey(const QString& keyId, const QString& filePath);
    Q_INVOKABLE bool importKey(const QString& filePath, const QString& keyName);

    // 文件加密操作
    Q_INVOKABLE QString encryptFile(const QString& filePath, const QString& outputDir = "", 
                                   bool keepOriginal = false, bool compressFirst = true);
    Q_INVOKABLE QString encryptDirectory(const QString& dirPath, const QString& outputDir = "",
                                        bool recursive = true, bool keepOriginal = false, 
                                        bool compressFirst = true);
    Q_INVOKABLE bool decryptFile(const QString& encryptedFilePath, const QString& outputDir = "");
    Q_INVOKABLE bool batchDecrypt(const QStringList& encryptedFilePaths, const QString& outputDir = "");

    // 任务管理
    Q_INVOKABLE QVariantList getEncryptionTasks() const;
    Q_INVOKABLE bool cancelTask(const QString& taskId);
    Q_INVOKABLE bool removeTask(const QString& taskId);
    Q_INVOKABLE void clearCompletedTasks();

    // 加密文件管理
    Q_INVOKABLE QVariantList getEncryptedFiles() const;
    Q_INVOKABLE bool removeEncryptedFile(const QString& filePath);
    Q_INVOKABLE QVariantMap getFileInfo(const QString& filePath) const;
    Q_INVOKABLE bool verifyFileIntegrity(const QString& filePath);

    // 配置管理
    Q_INVOKABLE void saveConfiguration() const;
    Q_INVOKABLE void loadConfiguration();
    Q_INVOKABLE QVariantMap getStatistics() const;

signals:
    void isEncryptingChanged();
    void encryptedFileCountChanged();
    void currentAlgorithmChanged();
    void encryptionProgress(const QString& taskId, int progress);
    void encryptionCompleted(const QString& taskId, bool success, const QString& message);
    void fileEncrypted(const QString& originalPath, const QString& encryptedPath);
    void fileDecrypted(const QString& encryptedPath, const QString& decryptedPath);
    void keyGenerated(const QString& keyId, const QString& keyName);
    void errorOccurred(const QString& message);

private slots:
    void onEncryptionTaskFinished();
    void updateProgress();

private:
    // 内部方法
    void initializeKeyStore();
    QString generateKeyId() const;
    QByteArray generateRandomKey(int keySize) const;
    bool saveKeyToStore(const KeyInfo& keyInfo, const QByteArray& keyData);
    QByteArray loadKeyFromStore(const QString& keyId) const;
    QString calculateFileChecksum(const QString& filePath) const;
    bool performFileEncryption(const QString& inputPath, const QString& outputPath, 
                              const QByteArray& key, EncryptionAlgorithm algorithm);
    bool performFileDecryption(const QString& inputPath, const QString& outputPath, 
                              const QByteArray& key, EncryptionAlgorithm algorithm);
    QByteArray compressData(const QByteArray& data) const;
    QByteArray decompressData(const QByteArray& data) const;
    void saveEncryptedFileInfo(const EncryptedFileInfo& info);
    void loadEncryptedFilesList();
    void saveEncryptedFilesList() const;
    EncryptionAlgorithm stringToAlgorithm(const QString& algorithmStr) const;
    QString algorithmToString(EncryptionAlgorithm algorithm) const;
    QString getDataDirectoryPath() const;

    // 成员变量
    bool m_isEncrypting;
    QString m_currentAlgorithm;
    QString m_keyStorePath;
    QString m_configPath;
    QString m_encryptedFilesPath;
    
    QList<KeyInfo> m_keys;
    QList<EncryptedFileInfo> m_encryptedFiles;
    QList<EncryptionTask> m_tasks;
    
    mutable QMutex m_mutex;
    QTimer* m_progressTimer;
    
    // 统计信息
    int m_totalEncryptedFiles;
    qint64 m_totalEncryptedSize;
    QDateTime m_lastEncryptionTime;
};

#endif // DATAENCRYPTIONENGINE_H
