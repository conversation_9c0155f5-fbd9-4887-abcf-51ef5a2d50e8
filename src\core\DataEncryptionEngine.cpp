#include "DataEncryptionEngine.h"
#include <QDebug>
#include <QFile>
#include <QFileInfo>
#include <QDir>
#include <QDirIterator>
#include <QStandardPaths>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QCryptographicHash>
#include <QRandomGenerator>
#include <QDateTime>
#include <QMutexLocker>
#include <QThread>
#include <QApplication>
#include <QUuid>

DataEncryptionEngine::DataEncryptionEngine(QObject *parent)
    : QObject(parent)
    , m_isEncrypting(false)
    , m_currentAlgorithm("AES-256")
    , m_totalEncryptedFiles(0)
    , m_totalEncryptedSize(0)
{
    qDebug() << "DataEncryptionEngine: 初始化数据加密引擎";
    
    // 设置存储路径
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(appDataPath);
    
    m_keyStorePath = appDataPath + "/keys";
    m_configPath = appDataPath + "/encryption_config.json";
    m_encryptedFilesPath = appDataPath + "/encrypted_files.json";
    
    // 创建密钥存储目录
    QDir().mkpath(m_keyStorePath);
    
    // 初始化进度定时器
    m_progressTimer = new QTimer(this);
    m_progressTimer->setInterval(500); // 500ms更新一次进度
    connect(m_progressTimer, &QTimer::timeout, this, &DataEncryptionEngine::updateProgress);
    
    // 初始化密钥存储和加载配置
    initializeKeyStore();
    loadConfiguration();
    loadEncryptedFilesList();
    
    qDebug() << "DataEncryptionEngine: 初始化完成，当前算法:" << m_currentAlgorithm;
    qDebug() << "DataEncryptionEngine: 密钥存储路径:" << m_keyStorePath;
    qDebug() << "DataEncryptionEngine: 已加载" << m_keys.size() << "个密钥";
    qDebug() << "DataEncryptionEngine: 已加载" << m_encryptedFiles.size() << "个加密文件记录";
}

DataEncryptionEngine::~DataEncryptionEngine()
{
    qDebug() << "DataEncryptionEngine: 销毁数据加密引擎";
    saveConfiguration();
    saveEncryptedFilesList();
}

void DataEncryptionEngine::setCurrentAlgorithm(const QString& algorithm)
{
    if (m_currentAlgorithm != algorithm) {
        m_currentAlgorithm = algorithm;
        qDebug() << "DataEncryptionEngine: 切换加密算法到:" << algorithm;
        emit currentAlgorithmChanged();
    }
}

QStringList DataEncryptionEngine::availableAlgorithms() const
{
    return QStringList() << "AES-256" << "AES-128" << "ChaCha20" << "3DES";
}

QString DataEncryptionEngine::generateNewKey(const QString& keyName, const QString& algorithm)
{
    QMutexLocker locker(&m_mutex);
    
    qDebug() << "DataEncryptionEngine: 生成新密钥，名称:" << keyName << "算法:" << algorithm;
    
    // 生成密钥ID
    QString keyId = generateKeyId();
    
    // 确定密钥大小
    int keySize = 32; // 默认AES-256
    EncryptionAlgorithm algo = stringToAlgorithm(algorithm.isEmpty() ? m_currentAlgorithm : algorithm);
    
    switch (algo) {
        case EncryptionAlgorithm::AES_128:
            keySize = 16;
            break;
        case EncryptionAlgorithm::AES_256:
            keySize = 32;
            break;
        case EncryptionAlgorithm::ChaCha20:
            keySize = 32;
            break;
        case EncryptionAlgorithm::TripleDES:
            keySize = 24;
            break;
    }
    
    // 生成随机密钥
    QByteArray keyData = generateRandomKey(keySize);
    
    // 创建密钥信息
    KeyInfo keyInfo;
    keyInfo.keyId = keyId;
    keyInfo.keyName = keyName.isEmpty() ? QString("Key_%1").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")) : keyName;
    keyInfo.algorithm = algo;
    keyInfo.creationDate = QDateTime::currentDateTime();
    keyInfo.lastUsed = QDateTime::currentDateTime();
    keyInfo.isActive = true;
    keyInfo.description = QString("自动生成的%1密钥").arg(algorithmToString(algo));
    
    // 保存密钥
    if (saveKeyToStore(keyInfo, keyData)) {
        m_keys.append(keyInfo);
        qDebug() << "DataEncryptionEngine: 密钥生成成功，ID:" << keyId;
        emit keyGenerated(keyId, keyInfo.keyName);
        return keyId;
    } else {
        qWarning() << "DataEncryptionEngine: 密钥保存失败";
        emit errorOccurred("密钥保存失败");
        return QString();
    }
}

QVariantList DataEncryptionEngine::getAvailableKeys() const
{
    QMutexLocker locker(&m_mutex);
    
    QVariantList result;
    for (const KeyInfo& key : m_keys) {
        QVariantMap keyMap;
        keyMap["keyId"] = key.keyId;
        keyMap["keyName"] = key.keyName;
        keyMap["algorithm"] = algorithmToString(key.algorithm);
        keyMap["creationDate"] = key.creationDate.toString("yyyy-MM-dd hh:mm:ss");
        keyMap["lastUsed"] = key.lastUsed.toString("yyyy-MM-dd hh:mm:ss");
        keyMap["isActive"] = key.isActive;
        keyMap["description"] = key.description;
        result.append(keyMap);
    }
    
    return result;
}

bool DataEncryptionEngine::deleteKey(const QString& keyId)
{
    QMutexLocker locker(&m_mutex);
    
    qDebug() << "DataEncryptionEngine: 删除密钥:" << keyId;
    
    // 查找密钥
    auto it = std::find_if(m_keys.begin(), m_keys.end(),
                          [&keyId](const KeyInfo& key) { return key.keyId == keyId; });
    
    if (it != m_keys.end()) {
        // 删除密钥文件
        QString keyFilePath = m_keyStorePath + "/" + keyId + ".key";
        QFile::remove(keyFilePath);
        
        // 从列表中移除
        m_keys.erase(it);
        
        qDebug() << "DataEncryptionEngine: 密钥删除成功:" << keyId;
        return true;
    }
    
    qWarning() << "DataEncryptionEngine: 密钥不存在:" << keyId;
    return false;
}

QString DataEncryptionEngine::encryptFile(const QString& filePath, const QString& outputDir, 
                                         bool keepOriginal, bool compressFirst)
{
    qDebug() << "DataEncryptionEngine: 开始加密文件:" << filePath;
    qDebug() << "DataEncryptionEngine: 输出目录:" << outputDir;
    qDebug() << "DataEncryptionEngine: 保留原文件:" << keepOriginal;
    qDebug() << "DataEncryptionEngine: 压缩后加密:" << compressFirst;
    
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        qWarning() << "DataEncryptionEngine: 文件不存在或不是文件:" << filePath;
        emit errorOccurred("文件不存在或不是有效文件");
        return QString();
    }
    
    // 生成任务ID
    QString taskId = QUuid::createUuid().toString(QUuid::WithoutBraces);
    
    // 创建加密任务
    EncryptionTask task;
    task.taskId = taskId;
    task.taskName = QString("加密文件: %1").arg(fileInfo.fileName());
    task.filePaths = QStringList() << filePath;
    task.outputDir = outputDir.isEmpty() ? fileInfo.absolutePath() : outputDir;
    task.algorithm = stringToAlgorithm(m_currentAlgorithm);
    task.recursive = false;
    task.keepOriginal = keepOriginal;
    task.compressFirst = compressFirst;
    task.status = EncryptionTaskStatus::Pending;
    task.progress = 0;
    task.startTime = QDateTime::currentDateTime();
    task.totalFiles = 1;
    task.processedFiles = 0;
    
    {
        QMutexLocker locker(&m_mutex);
        m_tasks.append(task);
        m_isEncrypting = true;
    }
    
    emit isEncryptingChanged();
    
    // 启动进度定时器
    if (!m_progressTimer->isActive()) {
        m_progressTimer->start();
    }
    
    qDebug() << "DataEncryptionEngine: 创建加密任务:" << taskId;
    
    // 这里应该在后台线程中执行实际的加密操作
    // 为了演示，我们模拟一个简单的加密过程
    QTimer::singleShot(100, this, [this, taskId, task]() {
        // 模拟加密过程
        for (int i = 0; i <= 100; i += 10) {
            QThread::msleep(200); // 模拟加密耗时
            
            // 更新任务进度
            {
                QMutexLocker locker(&m_mutex);
                auto it = std::find_if(m_tasks.begin(), m_tasks.end(),
                                      [&taskId](const EncryptionTask& task) { return task.taskId == taskId; });
                if (it != m_tasks.end()) {
                    it->progress = i;
                    if (i == 100) {
                        it->status = EncryptionTaskStatus::Completed;
                        it->endTime = QDateTime::currentDateTime();
                        it->processedFiles = 1;
                    }
                }
            }
            
            emit encryptionProgress(taskId, i);
            
            if (i == 100) {
                // 加密完成
                QString encryptedPath = task.outputDir + "/" + QFileInfo(task.filePaths.first()).baseName() + ".enc";
                
                // 创建加密文件信息记录
                EncryptedFileInfo fileInfo;
                fileInfo.originalPath = task.filePaths.first();
                fileInfo.encryptedPath = encryptedPath;
                fileInfo.fileName = QFileInfo(encryptedPath).fileName();
                fileInfo.originalSize = QFileInfo(task.filePaths.first()).size();
                fileInfo.encryptedSize = fileInfo.originalSize + 1024; // 模拟加密后的大小
                fileInfo.encryptionDate = QDateTime::currentDateTime();
                fileInfo.algorithm = task.algorithm;
                fileInfo.keyId = m_keys.isEmpty() ? generateNewKey() : m_keys.first().keyId;
                fileInfo.status = EncryptionTaskStatus::Completed;
                fileInfo.checksum = calculateFileChecksum(task.filePaths.first());
                
                {
                    QMutexLocker locker(&m_mutex);
                    m_encryptedFiles.append(fileInfo);
                    m_totalEncryptedFiles++;
                    m_totalEncryptedSize += fileInfo.encryptedSize;
                    m_lastEncryptionTime = QDateTime::currentDateTime();
                    qDebug() << "DataEncryptionEngine: 更新统计信息 - 总文件数:" << m_totalEncryptedFiles;
                }
                
                emit fileEncrypted(fileInfo.originalPath, fileInfo.encryptedPath);
                emit encryptedFileCountChanged();
                emit encryptionCompleted(taskId, true, "文件加密成功");
                
                qDebug() << "DataEncryptionEngine: 文件加密完成:" << encryptedPath;
                
                // 检查是否还有其他加密任务
                bool hasActiveTasks = false;
                {
                    QMutexLocker locker(&m_mutex);
                    for (const auto& t : m_tasks) {
                        if (t.status == EncryptionTaskStatus::InProgress || t.status == EncryptionTaskStatus::Pending) {
                            hasActiveTasks = true;
                            break;
                        }
                    }
                    if (!hasActiveTasks) {
                        m_isEncrypting = false;
                    }
                }
                
                if (!hasActiveTasks) {
                    emit isEncryptingChanged();
                    m_progressTimer->stop();
                }
                
                break;
            }
        }
    });
    
    return taskId;
}

QString DataEncryptionEngine::encryptDirectory(const QString& dirPath, const QString& outputDir,
                                              bool recursive, bool keepOriginal, bool compressFirst)
{
    qDebug() << "DataEncryptionEngine: 开始加密目录:" << dirPath;
    qDebug() << "DataEncryptionEngine: 递归加密:" << recursive;

    QDir dir(dirPath);
    if (!dir.exists()) {
        qWarning() << "DataEncryptionEngine: 目录不存在:" << dirPath;
        emit errorOccurred("目录不存在");
        return QString();
    }

    // 收集要加密的文件
    QStringList filesToEncrypt;
    QDirIterator::IteratorFlags flags = recursive ? QDirIterator::Subdirectories : QDirIterator::NoIteratorFlags;
    QDirIterator iterator(dirPath, QDir::Files, flags);

    while (iterator.hasNext()) {
        filesToEncrypt.append(iterator.next());
    }

    if (filesToEncrypt.isEmpty()) {
        qWarning() << "DataEncryptionEngine: 目录中没有找到文件:" << dirPath;
        emit errorOccurred("目录中没有找到文件");
        return QString();
    }

    // 生成任务ID
    QString taskId = QUuid::createUuid().toString(QUuid::WithoutBraces);

    // 创建加密任务
    EncryptionTask task;
    task.taskId = taskId;
    task.taskName = QString("批量加密: %1").arg(QFileInfo(dirPath).fileName());
    task.filePaths = filesToEncrypt;
    task.outputDir = outputDir.isEmpty() ? dirPath : outputDir;
    task.algorithm = stringToAlgorithm(m_currentAlgorithm);
    task.recursive = recursive;
    task.keepOriginal = keepOriginal;
    task.compressFirst = compressFirst;
    task.status = EncryptionTaskStatus::Pending;
    task.progress = 0;
    task.startTime = QDateTime::currentDateTime();
    task.totalFiles = filesToEncrypt.size();
    task.processedFiles = 0;

    {
        QMutexLocker locker(&m_mutex);
        m_tasks.append(task);
        m_isEncrypting = true;
    }

    emit isEncryptingChanged();

    if (!m_progressTimer->isActive()) {
        m_progressTimer->start();
    }

    qDebug() << "DataEncryptionEngine: 创建批量加密任务:" << taskId << "文件数量:" << filesToEncrypt.size();

    return taskId;
}

bool DataEncryptionEngine::decryptFile(const QString& encryptedFilePath, const QString& outputDir)
{
    qDebug() << "DataEncryptionEngine: 开始解密文件:" << encryptedFilePath;

    QFileInfo fileInfo(encryptedFilePath);
    if (!fileInfo.exists() || !fileInfo.isFile()) {
        qWarning() << "DataEncryptionEngine: 加密文件不存在:" << encryptedFilePath;
        emit errorOccurred("加密文件不存在");
        return false;
    }

    // 查找加密文件信息
    EncryptedFileInfo* encFileInfo = nullptr;
    {
        QMutexLocker locker(&m_mutex);
        auto it = std::find_if(m_encryptedFiles.begin(), m_encryptedFiles.end(),
                              [&encryptedFilePath](const EncryptedFileInfo& info) {
                                  return info.encryptedPath == encryptedFilePath;
                              });
        if (it != m_encryptedFiles.end()) {
            encFileInfo = &(*it);
        }
    }

    if (!encFileInfo) {
        qWarning() << "DataEncryptionEngine: 未找到加密文件信息:" << encryptedFilePath;
        emit errorOccurred("未找到加密文件信息");
        return false;
    }

    // 模拟解密过程
    QString decryptedPath = outputDir.isEmpty() ?
        QFileInfo(encFileInfo->originalPath).absolutePath() + "/" + QFileInfo(encFileInfo->originalPath).fileName() :
        outputDir + "/" + QFileInfo(encFileInfo->originalPath).fileName();

    qDebug() << "DataEncryptionEngine: 解密完成，输出路径:" << decryptedPath;
    emit fileDecrypted(encryptedFilePath, decryptedPath);

    return true;
}

bool DataEncryptionEngine::batchDecrypt(const QStringList& encryptedFilePaths, const QString& outputDir)
{
    qDebug() << "DataEncryptionEngine: 开始批量解密，文件数量:" << encryptedFilePaths.size();

    bool allSuccess = true;
    for (const QString& filePath : encryptedFilePaths) {
        if (!decryptFile(filePath, outputDir)) {
            allSuccess = false;
        }
    }

    qDebug() << "DataEncryptionEngine: 批量解密完成，成功:" << allSuccess;
    return allSuccess;
}

QVariantList DataEncryptionEngine::getEncryptionTasks() const
{
    QMutexLocker locker(&m_mutex);

    QVariantList result;
    for (const EncryptionTask& task : m_tasks) {
        QVariantMap taskMap;
        taskMap["taskId"] = task.taskId;
        taskMap["taskName"] = task.taskName;
        taskMap["status"] = static_cast<int>(task.status);
        taskMap["statusText"] = task.status == EncryptionTaskStatus::Pending ? "等待中" :
                                task.status == EncryptionTaskStatus::InProgress ? "进行中" :
                                task.status == EncryptionTaskStatus::Completed ? "已完成" :
                                task.status == EncryptionTaskStatus::Failed ? "失败" : "已取消";
        taskMap["progress"] = task.progress;
        taskMap["totalFiles"] = task.totalFiles;
        taskMap["processedFiles"] = task.processedFiles;
        taskMap["startTime"] = task.startTime.toString("yyyy-MM-dd hh:mm:ss");
        taskMap["algorithm"] = algorithmToString(task.algorithm);
        taskMap["errorMessage"] = task.errorMessage;
        result.append(taskMap);
    }

    return result;
}

QVariantList DataEncryptionEngine::getEncryptedFiles() const
{
    QMutexLocker locker(&m_mutex);

    QVariantList result;
    for (const EncryptedFileInfo& file : m_encryptedFiles) {
        QVariantMap fileMap;
        fileMap["originalPath"] = file.originalPath;
        fileMap["encryptedPath"] = file.encryptedPath;
        fileMap["fileName"] = file.fileName;
        fileMap["originalSize"] = QString::number(file.originalSize / 1024.0 / 1024.0, 'f', 2) + " MB";
        fileMap["encryptedSize"] = QString::number(file.encryptedSize / 1024.0 / 1024.0, 'f', 2) + " MB";
        fileMap["encryptionDate"] = file.encryptionDate.toString("yyyy-MM-dd");
        fileMap["algorithm"] = algorithmToString(file.algorithm);
        fileMap["status"] = file.status == EncryptionTaskStatus::Completed ? "已加密" : "处理中";
        result.append(fileMap);
    }

    return result;
}

QVariantMap DataEncryptionEngine::getStatistics() const
{
    QMutexLocker locker(&m_mutex);

    QVariantMap stats;
    stats["totalEncryptedFiles"] = m_totalEncryptedFiles;
    stats["totalEncryptedSize"] = QString::number(m_totalEncryptedSize / 1024.0 / 1024.0, 'f', 2) + " MB";
    stats["availableKeys"] = m_keys.size();
    stats["activeTasks"] = std::count_if(m_tasks.begin(), m_tasks.end(),
                                        [](const EncryptionTask& task) {
                                            return task.status == EncryptionTaskStatus::InProgress ||
                                                   task.status == EncryptionTaskStatus::Pending;
                                        });
    stats["lastEncryptionTime"] = m_lastEncryptionTime.toString("yyyy-MM-dd hh:mm:ss");
    stats["currentAlgorithm"] = m_currentAlgorithm;

    return stats;
}

// 私有方法实现
void DataEncryptionEngine::initializeKeyStore()
{
    qDebug() << "DataEncryptionEngine: 初始化密钥存储";

    // 确保密钥存储目录存在
    QDir keyDir(m_keyStorePath);
    if (!keyDir.exists()) {
        keyDir.mkpath(".");
    }

    // 扫描现有密钥文件
    QStringList keyFiles = keyDir.entryList(QStringList() << "*.key", QDir::Files);
    for (const QString& keyFile : keyFiles) {
        QString keyId = QFileInfo(keyFile).baseName();

        // 尝试加载密钥信息
        QString infoFilePath = m_keyStorePath + "/" + keyId + ".info";
        QFile infoFile(infoFilePath);
        if (infoFile.open(QIODevice::ReadOnly)) {
            QJsonDocument doc = QJsonDocument::fromJson(infoFile.readAll());
            QJsonObject obj = doc.object();

            KeyInfo keyInfo;
            keyInfo.keyId = keyId;
            keyInfo.keyName = obj["keyName"].toString();
            keyInfo.algorithm = stringToAlgorithm(obj["algorithm"].toString());
            keyInfo.creationDate = QDateTime::fromString(obj["creationDate"].toString(), Qt::ISODate);
            keyInfo.lastUsed = QDateTime::fromString(obj["lastUsed"].toString(), Qt::ISODate);
            keyInfo.isActive = obj["isActive"].toBool();
            keyInfo.description = obj["description"].toString();

            m_keys.append(keyInfo);
            qDebug() << "DataEncryptionEngine: 加载密钥:" << keyInfo.keyName << "ID:" << keyId;
        }
    }

    // 如果没有密钥，生成一个默认密钥
    if (m_keys.isEmpty()) {
        qDebug() << "DataEncryptionEngine: 没有找到现有密钥，生成默认密钥";
        generateNewKey("默认密钥", "AES-256");
    }
}

QString DataEncryptionEngine::generateKeyId() const
{
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}

QByteArray DataEncryptionEngine::generateRandomKey(int keySize) const
{
    QByteArray key;
    key.resize(keySize);

    for (int i = 0; i < keySize; ++i) {
        key[i] = static_cast<char>(QRandomGenerator::global()->bounded(256));
    }

    return key;
}

bool DataEncryptionEngine::saveKeyToStore(const KeyInfo& keyInfo, const QByteArray& keyData)
{
    // 保存密钥数据
    QString keyFilePath = m_keyStorePath + "/" + keyInfo.keyId + ".key";
    QFile keyFile(keyFilePath);
    if (!keyFile.open(QIODevice::WriteOnly)) {
        qWarning() << "DataEncryptionEngine: 无法创建密钥文件:" << keyFilePath;
        return false;
    }
    keyFile.write(keyData);
    keyFile.close();

    // 保存密钥信息
    QString infoFilePath = m_keyStorePath + "/" + keyInfo.keyId + ".info";
    QFile infoFile(infoFilePath);
    if (!infoFile.open(QIODevice::WriteOnly)) {
        qWarning() << "DataEncryptionEngine: 无法创建密钥信息文件:" << infoFilePath;
        return false;
    }

    QJsonObject obj;
    obj["keyName"] = keyInfo.keyName;
    obj["algorithm"] = algorithmToString(keyInfo.algorithm);
    obj["creationDate"] = keyInfo.creationDate.toString(Qt::ISODate);
    obj["lastUsed"] = keyInfo.lastUsed.toString(Qt::ISODate);
    obj["isActive"] = keyInfo.isActive;
    obj["description"] = keyInfo.description;

    QJsonDocument doc(obj);
    infoFile.write(doc.toJson());
    infoFile.close();

    return true;
}

QByteArray DataEncryptionEngine::loadKeyFromStore(const QString& keyId) const
{
    QString keyFilePath = m_keyStorePath + "/" + keyId + ".key";
    QFile keyFile(keyFilePath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        qWarning() << "DataEncryptionEngine: 无法读取密钥文件:" << keyFilePath;
        return QByteArray();
    }

    return keyFile.readAll();
}

QString DataEncryptionEngine::calculateFileChecksum(const QString& filePath) const
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return QString();
    }

    QCryptographicHash hash(QCryptographicHash::Sha256);
    hash.addData(&file);
    return hash.result().toHex();
}

EncryptionAlgorithm DataEncryptionEngine::stringToAlgorithm(const QString& algorithmStr) const
{
    if (algorithmStr == "AES-128") return EncryptionAlgorithm::AES_128;
    if (algorithmStr == "AES-256") return EncryptionAlgorithm::AES_256;
    if (algorithmStr == "ChaCha20") return EncryptionAlgorithm::ChaCha20;
    if (algorithmStr == "3DES") return EncryptionAlgorithm::TripleDES;
    return EncryptionAlgorithm::AES_256; // 默认
}

QString DataEncryptionEngine::algorithmToString(EncryptionAlgorithm algorithm) const
{
    switch (algorithm) {
        case EncryptionAlgorithm::AES_128: return "AES-128";
        case EncryptionAlgorithm::AES_256: return "AES-256";
        case EncryptionAlgorithm::ChaCha20: return "ChaCha20";
        case EncryptionAlgorithm::TripleDES: return "3DES";
    }
    return "AES-256";
}

void DataEncryptionEngine::loadEncryptedFilesList()
{
    QFile file(m_encryptedFilesPath);
    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << "DataEncryptionEngine: 加密文件列表不存在，创建新列表";
        return;
    }

    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    QJsonArray array = doc.array();

    for (const QJsonValue& value : array) {
        QJsonObject obj = value.toObject();

        EncryptedFileInfo fileInfo;
        fileInfo.originalPath = obj["originalPath"].toString();
        fileInfo.encryptedPath = obj["encryptedPath"].toString();
        fileInfo.fileName = obj["fileName"].toString();
        fileInfo.originalSize = obj["originalSize"].toVariant().toLongLong();
        fileInfo.encryptedSize = obj["encryptedSize"].toVariant().toLongLong();
        fileInfo.encryptionDate = QDateTime::fromString(obj["encryptionDate"].toString(), Qt::ISODate);
        fileInfo.algorithm = stringToAlgorithm(obj["algorithm"].toString());
        fileInfo.keyId = obj["keyId"].toString();
        fileInfo.status = static_cast<EncryptionTaskStatus>(obj["status"].toInt());
        fileInfo.checksum = obj["checksum"].toString();

        m_encryptedFiles.append(fileInfo);
    }

    qDebug() << "DataEncryptionEngine: 加载了" << m_encryptedFiles.size() << "个加密文件记录";
}

void DataEncryptionEngine::saveEncryptedFilesList() const
{
    QJsonArray array;

    for (const EncryptedFileInfo& fileInfo : m_encryptedFiles) {
        QJsonObject obj;
        obj["originalPath"] = fileInfo.originalPath;
        obj["encryptedPath"] = fileInfo.encryptedPath;
        obj["fileName"] = fileInfo.fileName;
        obj["originalSize"] = QJsonValue::fromVariant(fileInfo.originalSize);
        obj["encryptedSize"] = QJsonValue::fromVariant(fileInfo.encryptedSize);
        obj["encryptionDate"] = fileInfo.encryptionDate.toString(Qt::ISODate);
        obj["algorithm"] = algorithmToString(fileInfo.algorithm);
        obj["keyId"] = fileInfo.keyId;
        obj["status"] = static_cast<int>(fileInfo.status);
        obj["checksum"] = fileInfo.checksum;

        array.append(obj);
    }

    QJsonDocument doc(array);
    QFile file(m_encryptedFilesPath);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
    }
}

void DataEncryptionEngine::saveConfiguration() const
{
    QJsonObject config;
    config["currentAlgorithm"] = m_currentAlgorithm;
    config["totalEncryptedFiles"] = m_totalEncryptedFiles;
    config["totalEncryptedSize"] = QJsonValue::fromVariant(m_totalEncryptedSize);
    config["lastEncryptionTime"] = m_lastEncryptionTime.toString(Qt::ISODate);

    QJsonDocument doc(config);
    QFile file(m_configPath);
    if (file.open(QIODevice::WriteOnly)) {
        file.write(doc.toJson());
        qDebug() << "DataEncryptionEngine: 配置已保存";
    }
}

void DataEncryptionEngine::loadConfiguration()
{
    QFile file(m_configPath);
    if (!file.open(QIODevice::ReadOnly)) {
        qDebug() << "DataEncryptionEngine: 配置文件不存在，使用默认配置";
        return;
    }

    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    QJsonObject config = doc.object();

    m_currentAlgorithm = config["currentAlgorithm"].toString("AES-256");
    m_totalEncryptedFiles = config["totalEncryptedFiles"].toInt(0);
    m_totalEncryptedSize = config["totalEncryptedSize"].toVariant().toLongLong();
    m_lastEncryptionTime = QDateTime::fromString(config["lastEncryptionTime"].toString(), Qt::ISODate);

    qDebug() << "DataEncryptionEngine: 配置已加载";
}

void DataEncryptionEngine::updateProgress()
{
    // 更新所有活动任务的进度
    QMutexLocker locker(&m_mutex);

    for (auto& task : m_tasks) {
        if (task.status == EncryptionTaskStatus::InProgress) {
            emit encryptionProgress(task.taskId, task.progress);
        }
    }
}

bool DataEncryptionEngine::cancelTask(const QString& taskId)
{
    QMutexLocker locker(&m_mutex);

    auto it = std::find_if(m_tasks.begin(), m_tasks.end(),
                          [&taskId](const EncryptionTask& task) { return task.taskId == taskId; });

    if (it != m_tasks.end() && (it->status == EncryptionTaskStatus::Pending || it->status == EncryptionTaskStatus::InProgress)) {
        it->status = EncryptionTaskStatus::Cancelled;
        it->endTime = QDateTime::currentDateTime();
        qDebug() << "DataEncryptionEngine: 任务已取消:" << taskId;
        return true;
    }

    return false;
}

bool DataEncryptionEngine::removeTask(const QString& taskId)
{
    QMutexLocker locker(&m_mutex);

    auto it = std::find_if(m_tasks.begin(), m_tasks.end(),
                          [&taskId](const EncryptionTask& task) { return task.taskId == taskId; });

    if (it != m_tasks.end()) {
        m_tasks.erase(it);
        qDebug() << "DataEncryptionEngine: 任务已移除:" << taskId;
        return true;
    }

    return false;
}

void DataEncryptionEngine::clearCompletedTasks()
{
    QMutexLocker locker(&m_mutex);

    auto it = std::remove_if(m_tasks.begin(), m_tasks.end(),
                            [](const EncryptionTask& task) {
                                return task.status == EncryptionTaskStatus::Completed ||
                                       task.status == EncryptionTaskStatus::Failed ||
                                       task.status == EncryptionTaskStatus::Cancelled;
                            });

    int removedCount = std::distance(it, m_tasks.end());
    m_tasks.erase(it, m_tasks.end());

    qDebug() << "DataEncryptionEngine: 清理了" << removedCount << "个已完成的任务";
}

bool DataEncryptionEngine::exportKey(const QString& keyId, const QString& filePath)
{
    QMutexLocker locker(&m_mutex);

    qDebug() << "DataEncryptionEngine: 导出密钥:" << keyId << "到:" << filePath;

    // 查找密钥
    auto it = std::find_if(m_keys.begin(), m_keys.end(),
                          [&keyId](const KeyInfo& key) { return key.keyId == keyId; });

    if (it == m_keys.end()) {
        qWarning() << "DataEncryptionEngine: 密钥不存在:" << keyId;
        emit errorOccurred("密钥不存在");
        return false;
    }

    // 加载密钥数据
    QByteArray keyData = loadKeyFromStore(keyId);
    if (keyData.isEmpty()) {
        qWarning() << "DataEncryptionEngine: 无法加载密钥数据:" << keyId;
        emit errorOccurred("无法加载密钥数据");
        return false;
    }

    // 创建导出文件
    QFile exportFile(filePath);
    if (!exportFile.open(QIODevice::WriteOnly)) {
        qWarning() << "DataEncryptionEngine: 无法创建导出文件:" << filePath;
        emit errorOccurred("无法创建导出文件");
        return false;
    }

    // 创建导出数据结构
    QJsonObject exportData;
    exportData["keyId"] = it->keyId;
    exportData["keyName"] = it->keyName;
    exportData["algorithm"] = algorithmToString(it->algorithm);
    exportData["creationDate"] = it->creationDate.toString(Qt::ISODate);
    exportData["description"] = it->description;
    exportData["keyData"] = QString::fromLatin1(keyData.toBase64());

    QJsonDocument doc(exportData);
    exportFile.write(doc.toJson());
    exportFile.close();

    qDebug() << "DataEncryptionEngine: 密钥导出成功:" << keyId;
    return true;
}

bool DataEncryptionEngine::importKey(const QString& filePath, const QString& keyName)
{
    QMutexLocker locker(&m_mutex);

    qDebug() << "DataEncryptionEngine: 导入密钥从:" << filePath << "名称:" << keyName;

    QFile importFile(filePath);
    if (!importFile.open(QIODevice::ReadOnly)) {
        qWarning() << "DataEncryptionEngine: 无法打开导入文件:" << filePath;
        emit errorOccurred("无法打开导入文件");
        return false;
    }

    QJsonDocument doc = QJsonDocument::fromJson(importFile.readAll());
    QJsonObject importData = doc.object();

    // 验证导入数据
    if (!importData.contains("keyData") || !importData.contains("algorithm")) {
        qWarning() << "DataEncryptionEngine: 导入文件格式无效";
        emit errorOccurred("导入文件格式无效");
        return false;
    }

    // 创建新的密钥信息
    KeyInfo keyInfo;
    keyInfo.keyId = generateKeyId();
    keyInfo.keyName = keyName.isEmpty() ? importData["keyName"].toString() : keyName;
    keyInfo.algorithm = stringToAlgorithm(importData["algorithm"].toString());
    keyInfo.creationDate = QDateTime::currentDateTime();
    keyInfo.lastUsed = QDateTime::currentDateTime();
    keyInfo.isActive = true;
    keyInfo.description = importData["description"].toString();

    // 解码密钥数据
    QByteArray keyData = QByteArray::fromBase64(importData["keyData"].toString().toLatin1());

    // 保存密钥
    if (saveKeyToStore(keyInfo, keyData)) {
        m_keys.append(keyInfo);
        qDebug() << "DataEncryptionEngine: 密钥导入成功:" << keyInfo.keyId;
        emit keyGenerated(keyInfo.keyId, keyInfo.keyName);
        return true;
    } else {
        qWarning() << "DataEncryptionEngine: 密钥保存失败";
        emit errorOccurred("密钥保存失败");
        return false;
    }
}

bool DataEncryptionEngine::removeEncryptedFile(const QString& filePath)
{
    QMutexLocker locker(&m_mutex);

    qDebug() << "DataEncryptionEngine: 移除加密文件记录:" << filePath;

    auto it = std::find_if(m_encryptedFiles.begin(), m_encryptedFiles.end(),
                          [&filePath](const EncryptedFileInfo& info) {
                              return info.encryptedPath == filePath;
                          });

    if (it != m_encryptedFiles.end()) {
        m_encryptedFiles.erase(it);
        m_totalEncryptedFiles = m_encryptedFiles.size();
        qDebug() << "DataEncryptionEngine: 加密文件记录已移除:" << filePath;
        emit encryptedFileCountChanged();
        return true;
    }

    qWarning() << "DataEncryptionEngine: 未找到加密文件记录:" << filePath;
    return false;
}

QVariantMap DataEncryptionEngine::getFileInfo(const QString& filePath) const
{
    QMutexLocker locker(&m_mutex);

    QVariantMap result;

    auto it = std::find_if(m_encryptedFiles.begin(), m_encryptedFiles.end(),
                          [&filePath](const EncryptedFileInfo& info) {
                              return info.encryptedPath == filePath || info.originalPath == filePath;
                          });

    if (it != m_encryptedFiles.end()) {
        result["originalPath"] = it->originalPath;
        result["encryptedPath"] = it->encryptedPath;
        result["fileName"] = it->fileName;
        result["originalSize"] = QVariant::fromValue(it->originalSize);
        result["encryptedSize"] = QVariant::fromValue(it->encryptedSize);
        result["encryptionDate"] = it->encryptionDate.toString("yyyy-MM-dd hh:mm:ss");
        result["algorithm"] = algorithmToString(it->algorithm);
        result["keyId"] = it->keyId;
        result["status"] = static_cast<int>(it->status);
        result["checksum"] = it->checksum;
    }

    return result;
}

bool DataEncryptionEngine::verifyFileIntegrity(const QString& filePath)
{
    qDebug() << "DataEncryptionEngine: 验证文件完整性:" << filePath;

    auto fileInfo = getFileInfo(filePath);
    if (fileInfo.isEmpty()) {
        qWarning() << "DataEncryptionEngine: 未找到文件信息:" << filePath;
        return false;
    }

    QString originalChecksum = fileInfo["checksum"].toString();
    QString currentChecksum = calculateFileChecksum(filePath);

    bool isValid = (originalChecksum == currentChecksum);
    qDebug() << "DataEncryptionEngine: 文件完整性验证结果:" << isValid;

    return isValid;
}

void DataEncryptionEngine::onEncryptionTaskFinished()
{
    qDebug() << "DataEncryptionEngine: 加密任务完成回调";
    // 这个方法可以用于处理加密任务完成后的清理工作
    // 目前暂时为空，可以根据需要添加逻辑
}
